#include "bridgecontroller.h"
#include "serialmanager.h"
#include "mqttclientmanager.h"
#include "configmanager.h"
#include "log.h"

BridgeController::BridgeController(QObject *parent)
    : QObject(parent)
    , m_serialManager(nullptr)
    , m_mqttClientManager(nullptr)
{
    initialize();
}

BridgeController::~BridgeController()
{
    stop();
}

bool BridgeController::initialize()
{
    m_serialManager = new SerialManager(this);
    m_mqttClientManager = new MqttClientManager(this);

    // 连接串口管理器信号
    connect(m_serialManager, &SerialManager::atCommandReceived,
            this, &BridgeController::onAtCommandReceived);
    connect(m_serialManager, &SerialManager::atResponseReceived,
            this, &BridgeController::onAtResponseReceived);
    connect(m_serialManager, &SerialManager::urcReceived,
            this, &BridgeController::onUrcReceived);

    // 连接MQTT管理器信号
    //connect(m_mqttManager, &MqttManager::connectionStateChanged,
      //      this, &BridgeController::onMqttConnectionStateChanged);

    return true;
}

bool BridgeController::start()
{
    // 初始化日志模块
    LogManager::instance()->addLogger(&logInfo1);

    // 初始化配置管理器
    ConfigManager* configMgr = ConfigManager::instance();
    if(!configMgr->loadConfig())
    {
        return false;
    }

    // 启动串口管理器
    m_serialManager->initialize(configMgr->getSerialConfig());
    m_serialManager->start();

    return true;
}

void BridgeController::stop()
{
    m_serialManager->stop();
    ConfigManager::destroyInstance();
}

void BridgeController::onAtCommandReceived(const AtCommand& command)
{
    processAtCommand(command);
}

void BridgeController::onAtResponseReceived(const AtResponse& response)
{
    qDebug() << "收到AT响应:" << response.format();
}

void BridgeController::onUrcReceived(const AtResponse& response)
{
    qInfo() << "收到URC:" << response.format();
}

void BridgeController::onMqttConnectionStateChanged()
{
}

void BridgeController::onMqttMessageReceived(const QString& topic, const QByteArray& payload)
{
    qInfo() << "收到MQTT消息 - 主题:" << topic << "大小:" << payload.size();

    // 将MQTT消息转发为URC
    // QStringList params;
    // params << "0" << QString::number(generateMessageId()) << QString("\"%1\"").arg(topic)
    //        << QString::number(payload.size()) << QString("\"%1\"").arg(QString::fromUtf8(payload));

    // sendUrc(UrcType::QMTRECV, params);

}

void BridgeController::onMqttMessagePublished(const QString& topic, int messageId)
{
    // qInfo() << "MQTT消息发布成功 - 主题:" << topic << "消息ID:" << messageId;

    // 发送发布成功URC
    // sendUrc(UrcType::QMTPUB, QStringList() << "0" << QString::number(messageId) << "0");
}

void BridgeController::onMqttSubscribed(const QString& topic)
{
    // qInfo() << "MQTT订阅成功:" << topic;

    // 发送订阅成功URC
    // sendUrc(UrcType::QMTSUB, QStringList() << "0" << QString::number(generateMessageId()) << "0");
}

void BridgeController::onMqttUnsubscribed(const QString& topic)
{
    // qInfo() << "MQTT取消订阅成功:" << topic;

    // 发送取消订阅成功URC
    // sendUrc(UrcType::QMTUNSUB, QStringList() << "0" << QString::number(generateMessageId()));
}

void BridgeController::processAtCommand(const AtCommand& command)
{
    if (!command.isValid()) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return;
    }

    switch (command.getCommand()) {
    case MqttAtCommand::AT:
        // 基础AT指令
        sendAtResponse(AtResponse(AtResponseType::OK));
        break;

    case MqttAtCommand::QMTOPEN:
        processQmtOpen(command);
        break;

    case MqttAtCommand::QMTCLOSE:
        processQmtClose(command);
        break;

    case MqttAtCommand::QMTCONN:
        processQmtConn(command);
        break;

    case MqttAtCommand::QMTDISC:
        processQmtDisc(command);
        break;

    case MqttAtCommand::QMTSUB:
        processQmtSub(command);
        break;

    case MqttAtCommand::QMTUNSUB:
        processQmtUnsub(command);
        break;

    case MqttAtCommand::QMTPUB:
        processQmtPub(command);
        break;

    case MqttAtCommand::QMTPUBEX:
        processQmtPubEx(command);
        break;

    case MqttAtCommand::QMTCFG:
        processQmtCfg(command);
        break;

    default:
        logError("unsupported command: ") << command.format();
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        break;
    }
}

void BridgeController::sendAtResponse(const AtResponse& response)
{
    if (m_serialManager) {
        QString responseStr = response.format() + "\r\n";
        m_serialManager->sendRawData(responseStr.toUtf8());
        logTrace(QString("sendAtResponse: %1").arg(response.format()));
    }
}

void BridgeController::sendUrc(UrcType urcType, const QStringList& parameters)
{
    if (m_serialManager) {
        AtResponse urc;
        urc.setType(AtResponseType::URC);
        urc.setUrcType(urcType);
        urc.setParameters(parameters);

        QString urcStr = urc.format() + "\r\n";
        m_serialManager->sendRawData(urcStr.toUtf8());
        qDebug() << "发送URC:" << urc.format();
    }
}

bool BridgeController::processQmtOpen(const AtCommand& command)
{
    // AT+QMTOPEN=<client_idx>,"<host_name>",<port>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 3) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    QString host = command.getParameter(1);
    quint16 port = command.getParameter(2).toUInt();

    // 移除引号
    if (host.startsWith('"') && host.endsWith('"')) {
        host = host.mid(1, host.length() - 2);
    }

    logInfo(QString("processQmtOpen: %1 %2 %3").arg(clientIdx).arg(host).arg(port));
    // 尝试连接MQTT服务器
    bool success = m_mqttClientManager->handleQmtOpen(clientIdx, host, port);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        // 结果将通过URC异步通知
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtClose(const AtCommand& command)
{
    // AT+QMTCLOSE=<client_idx>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 1) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    logInfo(QString("processQmtClose: %1").arg(clientIdx));

    // 断开MQTT连接
    bool success = m_mqttClientManager->handleQmtClose(clientIdx);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtConn(const AtCommand& command)
{
    // AT+QMTCONN=<client_idx>,"<clientid>"[,<username>,<password>]
    if (command.getType() != AtCommandType::Set || command.getParameterCount() < 2) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    QString clientId = command.getParameter(1);
    QString username = command.getParameterCount() > 2 ? command.getParameter(2) : QString();
    QString password = command.getParameterCount() > 3 ? command.getParameter(3) : QString();

    // 移除引号
    if (clientId.startsWith('"') && clientId.endsWith('"')) {
        clientId = clientId.mid(1, clientId.length() - 2);
    }

    logInfo(QString("processQmtConn: %1 %2").arg(clientIdx).arg(clientId));

    bool success = m_mqttClientManager->handleQmtConn(clientIdx, clientId, username, password);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtDisc(const AtCommand& command)
{
    // AT+QMTDISC=<client_idx>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 1) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    logInfo(QString("processQmtDisc: %1").arg(clientIdx));

    // 断开MQTT连接
    bool success = m_mqttClientManager->handleQmtDisc(clientIdx);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtSub(const AtCommand& command)
{
    // AT+QMTSUB=<client_idx>,<msgid>,"<topic>",<qos>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 4) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    QString topic = command.getParameter(2);
    int qos = command.getParameter(3).toInt();

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    logInfo(QString("processQmtSub: %1 %2 %3 %4").arg(clientIdx).arg(msgId).arg(topic).arg(qos));

    // 订阅MQTT主题
    bool success = m_mqttClientManager->handleQmtSub(clientIdx, msgId, topic, qos);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtUnsub(const AtCommand& command)
{
    // AT+QMTUNS=<client_idx>,<msgid>,"<topic>"
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 3) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    QString topic = command.getParameter(2);

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    logInfo(QString("processQmtUnsub: %1 %2 %3").arg(clientIdx).arg(msgId).arg(topic));

    // 取消订阅MQTT主题
    //bool success = m_mqttClientManager->handleQmtUnsub(clientIdx, msgId, topic);

    // if (success) {
    //     sendAtResponse(AtResponse(AtResponseType::OK));
    //     return true;
    // } else {
    //     sendAtResponse(AtResponse(AtResponseType::ERROR));
    //     return false;
    // }
    return true;
}

bool BridgeController::processQmtPub(const AtCommand& command)
{
    // AT+QMTPUB=<client_idx>,<msgid>,<qos>,<retain>,"<topic>","<msg>"
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 6) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    int qos = command.getParameter(2).toInt();
    bool retain = command.getParameter(3).toInt() != 0;
    QString topic = command.getParameter(4);
    QString message = command.getParameter(5);

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }
    if (message.startsWith('"') && message.endsWith('"')) {
        message = message.mid(1, message.length() - 2);
    }

    logInfo(QString("processQmtPub: %1 %2 %3 %4 %5 %6").arg(clientIdx).arg(msgId).arg(qos).arg(retain).arg(topic).arg(message));

    // 发布MQTT消息
    QByteArray payload = message.toUtf8();
    bool success = m_mqttClientManager->handleQmtPub(clientIdx, msgId, qos, retain, topic, payload);

    if (success) {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}

bool BridgeController::processQmtPubEx(const AtCommand& command)
{
    // AT+QMTPUBEX=<client_idx>,<msgid>,<qos>,<retain>,"<topic>",<msg_length>
    if (command.getType() != AtCommandType::Set || command.getParameterCount() != 6) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    int clientIdx = command.getParameter(0).toInt();
    int msgId = command.getParameter(1).toInt();
    int qos = command.getParameter(2).toInt();
    bool retain = command.getParameter(3).toInt() != 0;
    QString topic = command.getParameter(4);
    int msgLength = command.getParameter(5).toInt();

    // 移除引号
    if (topic.startsWith('"') && topic.endsWith('"')) {
        topic = topic.mid(1, topic.length() - 2);
    }

    logInfo(QString("processQmtPubEx: %1 %2 %3 %4 %5 %6").arg(clientIdx).arg(msgId).arg(qos).arg(retain).arg(topic).arg(msgLength));

    // QMTPUBEX需要等待后续的数据输入，这里简化处理
    // 实际实现中需要发送">"提示符，然后等待指定长度的数据
    sendAtResponse(AtResponse(AtResponseType::Prompt));

    // 这里应该等待数据输入，简化实现直接返回成功
    return true;
}

bool BridgeController::processQmtCfg(const AtCommand& command)
{
    // AT+QMTCFG="<cfg_type>",<client_idx>[,<cfg_value>]
    if (command.getType() != AtCommandType::Set || command.getParameterCount() < 2) {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }

    QString cfgType = command.getParameter(0);
    int clientIdx = command.getParameter(1).toInt();

    // 移除引号
    if (cfgType.startsWith('"') && cfgType.endsWith('"')) {
        cfgType = cfgType.mid(1, cfgType.length() - 2);
    }

    logInfo(QString("processQmtCfg: %1 %2").arg(cfgType).arg(clientIdx));

    // 简化实现，支持基本的配置类型
    if (cfgType == "version" || cfgType == "keepalive" || cfgType == "session") {
        sendAtResponse(AtResponse(AtResponseType::OK));
        return true;
    } else {
        sendAtResponse(AtResponse(AtResponseType::ERROR));
        return false;
    }
}
